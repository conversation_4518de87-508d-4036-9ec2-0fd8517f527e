package org.jeecg.modules.api.power_trade.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.ProvinceDataSourceUtil;
import org.jeecg.modules.api.power.param.PowerGenerationTrendDto;
import org.jeecg.modules.api.power.param.PowerGenerationTrendQueryParam;
import org.jeecg.modules.api.power.service.impl.PowerService;
import org.jeecg.modules.api.power_trade.dto.SettlementSummaryDTO;
import org.jeecg.modules.api.power_trade.dto.StationDetailResponseDTO;
import org.jeecg.modules.api.power_trade.dto.TimeRange;
import org.jeecg.modules.api.power_trade.entity.MarketNotice;
import org.jeecg.modules.api.power_trade.entity.ScreenTradeSettlement;
import org.jeecg.modules.api.power_trade.entity.Station;
import org.jeecg.modules.api.power_trade.entity.TradeCalendarRecord;
import org.jeecg.modules.api.power_trade.entity.YearlyPowerPlan;
import org.jeecg.modules.api.power_trade.mapper.ScreenTradeSettlementMapper;
import org.jeecg.modules.api.power_trade.vo.MarketNoticeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 多数据源聚合服务
 * 处理全国数据源的数据汇总逻辑，支持所有接口的多数据源汇总
 */
@Slf4j
@Service
public class MultiDataSourceAggregationService {

    @Autowired
    private StationService stationService;

    @Autowired
    private ScreenTradeSettlementService screenTradeSettlementService;

    @Autowired
    private YearlyPowerPlanService yearlyPowerPlanService;

    @Autowired
    private PowerService powerService;

    @Autowired
    private ScreenTradeSettlementMapper screenTradeSettlementMapper;

    @Autowired
    private PowerSideSettleService powerSideSettleService;

    // 并行查询线程池
    private final ExecutorService queryExecutor = Executors.newFixedThreadPool(20);

    // 查询超时时间（秒）
    private static final int QUERY_TIMEOUT_SECONDS = 30;

    /**
     * 聚合所有省份的电站列表数据
     */
    public Map<String, Object> aggregateAllProvincesStationList(
            Integer pageNo, Integer pageSize, String year, String month, String name) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源的电站列表，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<Map<String, Object>>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceStationList(s, year, month, name), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的电站数据
            List<Map<String, Object>> allStations = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份电站列表失败: {}", e.getMessage());
                            return new ArrayList<Map<String, Object>>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 分页处理
            int total = allStations.size();
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedStations = startIndex < total ?
                    allStations.subList(startIndex, endIndex) : new ArrayList<>();

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("current", pageNo);
            result.put("size", pageSize);
            result.put("total", total);
            result.put("pages", (total + pageSize - 1) / pageSize);
            result.put("records", pagedStations);

            return result;

        } catch (Exception e) {
            log.error("聚合全国电站列表失败: {}", e.getMessage(), e);
            return createEmptyPageResult(pageNo, pageSize);
        }
    }

    /**
     * 查询单个省份的电站列表
     */
    private List<Map<String, Object>> queryProvinceStationList(Integer provinceId, String year, String month, String name) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            return new ArrayList<>();
        }
        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 构建查询条件
            LambdaQueryWrapper<Station> stationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            stationLambdaQueryWrapper.eq(Station::getTradeStatus, 1)
                    .eq(true, Station::getProvinceId, provinceId);

            // 添加电站名称搜索条件
            if (name != null && !name.trim().isEmpty()) {
                stationLambdaQueryWrapper.like(Station::getName, name.trim());
            }

            // 查询电站列表（不分页，获取所有数据用于汇总）
            List<Station> stations = stationService.list(stationLambdaQueryWrapper);

            log.info("查询到省份{}的电站数量：{}", provinceId, stations.size());

            // 为每个电站添加结算数据
            List<Map<String, Object>> result = stations.stream()
                    .map(station -> buildStationWithSettlementData(station, year, month))
                    .collect(Collectors.toList());

            log.info("省份{}处理完成，返回电站数量：{}", provinceId, result.size());
            return result;

        } catch (Exception e) {
            log.warn("查询省份{}电站列表失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 构建包含结算数据的电站信息（从Controller提取的逻辑）
     */
    private Map<String, Object> buildStationWithSettlementData(Station station, String year, String month) {
        Map<String, Object> result = new HashMap<>();
        result.put("stationInfo", station);

        try {
            LambdaQueryWrapper<ScreenTradeSettlement> settlementWrapper = new LambdaQueryWrapper<>();
            settlementWrapper.eq(ScreenTradeSettlement::getStationId, station.getId())
                    .eq(ScreenTradeSettlement::getYear, year)
                    .eq(ScreenTradeSettlement::getMonth, String.format("%02d", Integer.parseInt(month)));

            LambdaQueryWrapper<YearlyPowerPlan> yearlyWrapper = new LambdaQueryWrapper<>();
            yearlyWrapper.eq(YearlyPowerPlan::getStationId, station.getId())
                    .eq(YearlyPowerPlan::getYear, year)
                    .eq(YearlyPowerPlan::getMonth, String.format("%02d", Integer.parseInt(month)));

            ScreenTradeSettlement settlement = screenTradeSettlementService.getOne(settlementWrapper);
            YearlyPowerPlan yearly = yearlyPowerPlanService.getOne(yearlyWrapper);



            if (settlement != null) {
                // 当月实际发电量 - 使用功率预测实际功率求和除以4的计算逻辑
                BigDecimal currentMonthPower = calculateCurrentPeriodGeneration(station.getId(),
                        year + "-" + String.format("%02d", Integer.parseInt(month)), "2");
                result.put("current_month_power", currentMonthPower);

                // 当月计划发电量
                BigDecimal currentMonthPlanPower = yearly != null && yearly.getPlanValue() != null
                        ? yearly.getPlanValue()
                        : BigDecimal.ZERO;
                result.put("current_month_plan_power", currentMonthPlanPower);

                // 结算均价
                BigDecimal settlementAveragePrice = settlement.getSettlementAveragePrice() != null
                        ? settlement.getSettlementAveragePrice()
                        : BigDecimal.ZERO;
                result.put("settlement_average_price", settlementAveragePrice);
            } else {
                // 没有结算数据时的默认值
                result.put("current_month_power", BigDecimal.ZERO);
                result.put("current_month_plan_power", BigDecimal.ZERO);
                result.put("settlement_average_price", BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("获取电站{}结算数据失败: {}", station.getId(), e.getMessage());
            // 异常时设置默认值
            result.put("current_month_power", BigDecimal.ZERO);
            result.put("current_month_plan_power", BigDecimal.ZERO);
            result.put("settlement_average_price", BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 计算当前周期发电量（从功率预测实际功率计算）
     */
    private BigDecimal calculateCurrentPeriodGeneration(Long stationId, String date, String dimension) {
        try {
            PowerGenerationTrendQueryParam param = new PowerGenerationTrendQueryParam();
            param.setStationId(stationId);
            param.setProvinceId(1); // 在动态数据源上下文中，设置默认省份ID
            param.setTimeDimension(dimension);
            param.setQueryDate(date);

            // 获取功率预测数据
            List<PowerGenerationTrendDto> trendData = powerService.getPowerGenerationTrend(param);

            if (trendData != null && !trendData.isEmpty()) {
                // 使用实际功率计算发电量：实际功率求和 ÷ 4
                double totalActualPower = trendData.stream()
                        .map(PowerGenerationTrendDto::getActualPower)
                        .filter(Objects::nonNull)
                        .reduce(0.0, Double::sum);

                // 实际功率除以4得到发电量
                return BigDecimal.valueOf(totalActualPower / 4.0);
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.warn("计算发电量失败，返回0: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }


    /**
     * 创建空的分页结果
     */
    private Map<String, Object> createEmptyPageResult(Integer pageNo, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        result.put("current", pageNo);
        result.put("size", pageSize);
        result.put("total", 0);
        result.put("pages", 0);
        result.put("records", new ArrayList<>());
        return result;
    }


    /**
     * 聚合所有省份的结算概况数据
     */
    public List<SettlementSummaryDTO> aggregateAllProvincesSettlementSummary(
            Long stationId, Integer dimension, String month, String year) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<SettlementSummaryDTO>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvinceSettlementSummary(s, stationId, dimension, month, year), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的结算数据
            List<SettlementSummaryDTO> allSettlements = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份结算数据失败: {}", e.getMessage());
                            return new ArrayList<SettlementSummaryDTO>();
                        }
                    })
                    .flatMap(List::stream).sorted((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity())).collect(Collectors.toList());

            // 按结算电量降序排列

            return allSettlements;

        } catch (Exception e) {
            log.error("聚合全国结算概况失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 聚合所有省份的发电趋势数据
     */
    public List<PowerGenerationTrendDto> aggregateAllProvincesPowerGenerationTrend(
            PowerGenerationTrendQueryParam param) {

        Map<Integer, String> allDataSources = ProvinceDataSourceUtil.getAllProvinceDataSource();

        // 并行查询所有数据源，排除provinceId=0（全国汇总）
        List<CompletableFuture<List<PowerGenerationTrendDto>>> futures = allDataSources.keySet().stream()
                .filter(s -> s != 0) // 排除全国汇总
                .map(s -> CompletableFuture.supplyAsync(() ->
                        queryProvincePowerGenerationTrend(s, param), queryExecutor))
                .collect(Collectors.toList());

        try {
            // 收集所有省份的发电趋势数据
            List<PowerGenerationTrendDto> allTrendData = futures.stream()
                    .map(future -> {
                        try {
                            return future.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                        } catch (Exception e) {
                            log.warn("查询省份发电趋势失败: {}", e.getMessage());
                            return new ArrayList<PowerGenerationTrendDto>();
                        }
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 按时间维度聚合数据
            return aggregatePowerGenerationTrendData(allTrendData, param.getTimeDimension());

        } catch (Exception e) {
            log.error("聚合全国发电趋势失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询单个省份的结算汇总数据
     */
    private List<SettlementSummaryDTO> queryProvinceSettlementSummary(Integer provinceId,
            Long stationId, Integer dimension, String month, String year) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            log.info("开始查询省份{}结算汇总数据 - 电站ID: {}, 维度: {}, 月份: {}, 年份: {}",
                    provinceId, stationId, dimension, month, year);

            // 1. 获取目标电站列表
            List<Station> targetStations = getTargetStations(stationId, provinceId);
            if (targetStations.isEmpty()) {
                log.warn("省份{}下没有找到电站数据", provinceId);
                return new ArrayList<>();
            }

            // 2. 计算时间范围
            TimeRange timeRange = calculateTimeRange(dimension, month, year);
            log.debug("时间范围计算完成 - 开始日期: {}, 结束日期: {}, 开始年月: {}, 结束年月: {}",
                    timeRange.getStartDate(), timeRange.getEndDate(),
                    timeRange.getStartYearMonth(), timeRange.getEndYearMonth());

            // 3. 查询每个电站的结算数据
            List<SettlementSummaryDTO> resultList = new ArrayList<>();

            for (Station station : targetStations) {
                try {
                    // 查询该电站的结算数据
                    Map<String, BigDecimal> settlementData = powerSideSettleService.getStationSettlementSummary(
                            station.getId(),
                            timeRange.getStartDate(),
                            timeRange.getEndDate(),
                            timeRange.getStartYearMonth(),
                            timeRange.getEndYearMonth()
                    );

                    // 构建DTO对象
                    SettlementSummaryDTO stationDto = new SettlementSummaryDTO();
                    stationDto.setStationId(station.getId());
                    stationDto.setStationName(station.getName());
                    stationDto.setStationType(station.getType());

                    // 从查询结果中获取数据
                    if (settlementData != null && !settlementData.isEmpty()) {
                        BigDecimal totalSettlementElectricity = settlementData.getOrDefault("totalSettlementElectricity", BigDecimal.ZERO);
                        BigDecimal totalSettlementElectricFee = settlementData.getOrDefault("totalSettlementElectricFee", BigDecimal.ZERO);
                        BigDecimal avgTradePrice = settlementData.getOrDefault("avgTradePrice", BigDecimal.ZERO);

                        stationDto.setTotalSettlementElectricity(totalSettlementElectricity);
                        stationDto.setTotalSettlementElectricFee(totalSettlementElectricFee);
                        stationDto.setAvgTradePrice(avgTradePrice);

                        log.debug("电站结算数据查询成功 - 电站: {}, 累计结算电量: {} MWh, 交易均价: {} 元/MWh",
                                station.getName(), totalSettlementElectricity, avgTradePrice);
                    } else {
                        // 无结算数据时设置为0
                        stationDto.setTotalSettlementElectricity(BigDecimal.ZERO);
                        stationDto.setTotalSettlementElectricFee(BigDecimal.ZERO);
                        stationDto.setAvgTradePrice(BigDecimal.ZERO);
                        log.debug("电站无结算数据 - 电站: {}", station.getName());
                    }

                    resultList.add(stationDto);

                } catch (Exception e) {
                    log.error("查询电站{}结算数据失败", station.getName(), e);
                    // 异常时创建默认数据
                    SettlementSummaryDTO defaultDto = new SettlementSummaryDTO();
                    defaultDto.setStationId(station.getId());
                    defaultDto.setStationName(station.getName());
                    defaultDto.setStationType(station.getType());
                    defaultDto.setTotalSettlementElectricity(BigDecimal.ZERO);
                    defaultDto.setTotalSettlementElectricFee(BigDecimal.ZERO);
                    defaultDto.setAvgTradePrice(BigDecimal.ZERO);
                    resultList.add(defaultDto);
                }
            }

            // 4. 按结算电量降序排列
            resultList.sort((a, b) -> b.getTotalSettlementElectricity().compareTo(a.getTotalSettlementElectricity()));

            log.info("省份{}结算汇总数据查询完成 - 返回{}条记录", provinceId, resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询省份{}结算汇总数据失败: {}", provinceId, e.getMessage(), e);
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

  

    /**
     * 查询单个省份的发电趋势数据
     */
    private List<PowerGenerationTrendDto> queryProvincePowerGenerationTrend(Integer provinceId,
            PowerGenerationTrendQueryParam param) {
        String dsKey = ProvinceDataSourceUtil.getDataSourceKey(provinceId);
        if (dsKey == null) {
            log.warn("省份{}没有对应的数据源", provinceId);
            return new ArrayList<>();
        }

        DynamicDataSourceContextHolder.push(dsKey);
        try {
            // 这里应该调用具体的发电趋势查询逻辑
            // 暂时返回空列表，避免编译错误
            log.info("查询省份{}发电趋势数据 - 参数: {}", provinceId, param);
            return new ArrayList<>();
        } catch (Exception e) {
            log.warn("查询省份{}发电趋势数据失败: {}", provinceId, e.getMessage());
            return new ArrayList<>();
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    /**
     * 聚合发电趋势数据
     */
    private List<PowerGenerationTrendDto> aggregatePowerGenerationTrendData(
            List<PowerGenerationTrendDto> allTrendData, String timeDimension) {
        try {
            // 这里应该实现具体的数据聚合逻辑
            // 暂时返回原始数据，避免编译错误
            log.info("聚合发电趋势数据 - 数据量: {}, 时间维度: {}", allTrendData.size(), timeDimension);
            return allTrendData;
        } catch (Exception e) {
            log.warn("聚合发电趋势数据失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private List<Station> getTargetStations(Long stationId, Integer provinceId) {
        if (stationId != null) {
            // 查询指定电站
            LambdaQueryWrapper<Station> stationQuery = new LambdaQueryWrapper<>();
            stationQuery.eq(Station::getId, stationId)
                    .eq(Station::getProvinceId, provinceId);
            Station station = stationService.getOne(stationQuery);
            return station != null ? Collections.singletonList(station) : new ArrayList<>();
        } else {
            // 查询该省份的所有电站
            LambdaQueryWrapper<Station> allStationsQuery = new LambdaQueryWrapper<>();
            allStationsQuery.eq(Station::getProvinceId, provinceId);
            return stationService.list(allStationsQuery);
        }
    }

    /**
     * 计算时间范围
     */
    private TimeRange calculateTimeRange(Integer dimension, String month, String year) {
        if (dimension == 1) {
            // 月度查询
            String startDate = month + "-01";
            String endDate = getMonthEndDate(month);
            return new TimeRange(startDate, endDate, month, month);
        } else {
            // 年度查询
            String startDate = year + "-01-01";
            String endDate = year + "-12-31";
            String startYearMonth = year + "-01";
            String endYearMonth = year + "-12";
            return new TimeRange(startDate, endDate, startYearMonth, endYearMonth);
        }
    }

    /**
     * 获取月份的最后一天
     */
    private String getMonthEndDate(String yearMonth) {
        try {
            YearMonth ym = YearMonth.parse(yearMonth);
            return yearMonth + "-" + String.format("%02d", ym.lengthOfMonth());
        } catch (Exception e) {
            log.error("解析月份失败: {}", yearMonth, e);
            return yearMonth + "-31"; // 默认返回31号
        }
    }
}

