package org.jeecg.modules.api.power_trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.api.power_trade.entity.PolicyFile;
import org.jeecg.modules.api.power_trade.mapper.PolicyFileMapper;
import org.jeecg.modules.api.power_trade.service.PolicyFileService;
import org.jeecg.modules.api.power_trade.vo.PolicyFileVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PolicyFileServiceImpl extends ServiceImpl<PolicyFileMapper, PolicyFile> implements PolicyFileService {

    @Value("${jeecg.minio.externalEndpoint}")
    private String externalEndpoint;

    @Override
    public IPage<PolicyFileVO> getPolicyFileList(Page<PolicyFile> page, Integer provinceId, String keyword) {
        log.info("查询政策文件列表 - 省份ID: {}, 关键词: {}", provinceId, keyword);

        // 构建查询条件
        LambdaQueryWrapper<PolicyFile> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(PolicyFile::getFileName, keyword);
        }

        // 查询分页数据
        IPage<PolicyFile> policyFilePage = this.page(page, queryWrapper);

        // 转换为VO
        List<PolicyFileVO> voList = policyFilePage.getRecords().stream().map(policyFile -> {
            PolicyFileVO vo = new PolicyFileVO();
            BeanUtils.copyProperties(policyFile, vo);

            // 设置完整URL
            if (StringUtils.isNotBlank(policyFile.getFileUrl())) {
                vo.setFileUrl(externalEndpoint + policyFile.getFileUrl());
            }

            // 设置省份信息
            vo.setProvinceId(provinceId);

            return vo;
        }).collect(Collectors.toList());

        // 构建返回结果
        Page<PolicyFileVO> resultPage = new Page<>(page.getCurrent(), page.getSize(), policyFilePage.getTotal());
        resultPage.setRecords(voList);

        return resultPage;
    }
}
